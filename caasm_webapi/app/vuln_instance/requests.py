from typing import List
from caasm_service.entity.category_view import QueryFilterEntity
from caasm_webapi.app.query_engine.requests.entity import QueryAttributeRequest


class VulnQueryAttributeRequest(QueryAttributeRequest):
    def __init__(self):
        super().__init__()
        self.attribute_filters: List[QueryFilterEntity] = []


class VulnAggsWithVulnNameRequest(QueryAttributeRequest):
    def __init__(self):
        super().__init__()
        self.fields = []


class VulnPriorityTrendRequest:
    def __init__(self):
        self.start_time = None
        self.end_time = None
