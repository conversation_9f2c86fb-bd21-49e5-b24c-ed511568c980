from rest_framework import fields
from rest_framework.serializers import Serializer
from datetime import datetime

from caasm_webapi.app.query_engine.serializers.entity import (
    QueryAttributeRequestSerializer,
    QueryFilterRequestSerializer,
)
from caasm_webapi.app.vuln_instance.requests import (
    VulnQueryAttributeRequest,
    VulnAggsWithVulnNameRequest,
    VulnPriorityTrendRequest,
)


class VulnQueryAttributeRequestSerializer(QueryAttributeRequestSerializer):
    _request_class = VulnQueryAttributeRequest

    __attibute_query = [
        "vul_instance_unique.status",
        "vul_instance_unique.priority",
        "vul_instance_unique.severity",
        "vul_instance_unique.response_status",
    ]
    _attribute_mapper = {
        "vul_instance_unique.status": "漏洞状态",
        "vul_instance_unique.priority": "漏洞修复优先级",
        "vul_instance_unique.severity": "漏洞严重程度",
        "vul_instance_unique.response_status": "漏洞在册状态",
    }

    def create(self, validated_data):
        req: VulnQueryAttributeRequest = super().create(validated_data)
        for q in self.__attibute_query:
            serilizer = QueryFilterRequestSerializer(
                data={
                    "title": f"{self._attribute_mapper[q]}",
                    "type": "attribute_filter",
                    "config": {"field": f"{q}", "selectAll": False},
                    "filter_id": f"QueryFilterType.ATTRIBUTE:{q}",
                    "selectedItems": [],
                    "show": False,
                    "item": [],
                }
            )
            if serilizer.is_valid():
                req.attribute_filters.append(serilizer.save())
        return req


class VulnLevelDistributionRequestSerializer(QueryAttributeRequestSerializer):
    def create(self, validated_data):
        req = super().create(validated_data)
        serilizer = QueryFilterRequestSerializer(
            data={
                "title": "漏洞严重程度",
                "type": "attribute_filter",
                "config": {"field": "vul_instance_unique.severity", "selectAll": False},
                "filter_id": f"QueryFilterType.ATTRIBUTE:vul_instance_unique.severity",
                "selectedItems": [],
                "show": False,
                "item": [],
            }
        )
        if serilizer.is_valid():
            req.attribute_filter = serilizer.save()
        return req


class VulnInstanceAggeWithVulnNameRequestSerializer(QueryAttributeRequestSerializer):
    _request_class = VulnAggsWithVulnNameRequest

    fields = fields.ListField(child=fields.CharField(required=True), required=False, help_text="需要聚合的字段")

    def create(self, validated_data):
        req: VulnAggsWithVulnNameRequest = super().create(validated_data)
        req.fields = [
            "vul_instance_unique.cve_id",
            "vul_instance_unique.name",
            "vul_instance_unique.severity",
        ]
        return req


class VulnTotalViewRequestSerializer(Serializer):
    pass


class VulnPriorityTrendRequestSerializer(Serializer):
    start_time = fields.IntegerField(required=False, help_text="开始时间戳(毫秒)")
    end_time = fields.IntegerField(required=False, help_text="结束时间戳(毫秒)")

    def validate_start_time(self, value):
        try:
            datetime.fromtimestamp(value / 1000)
        except (ValueError, OSError):
            raise fields.ValidationError("开始时间戳格式无效")
        return value

    def validate_end_time(self, value):
        try:
            datetime.fromtimestamp(value / 1000)
        except (ValueError, OSError):
            raise fields.ValidationError("结束时间戳格式无效")
        return value

    def validate(self, attrs):
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")

        if start_time and end_time and start_time >= end_time:
            raise fields.ValidationError("开始时间必须小于结束时间")

        return attrs

    def create(self, validated_data):
        req = VulnPriorityTrendRequest()
        if validated_data.get("start_time"):
            req.start_time = datetime.fromtimestamp(validated_data["start_time"] / 1000)
        if validated_data.get("end_time"):
            req.end_time = datetime.fromtimestamp(validated_data["end_time"] / 1000)
        return req
